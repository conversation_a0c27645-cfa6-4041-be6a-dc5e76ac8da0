import os
import requests
import pandas as pd
import datetime
import pytz
from dhanhq import dhanhq
import time


def get_instrument_file():
		global instrument_df
		current_date = time.strftime("%Y-%m-%d")
		expected_file = 'all_instrument ' + str(current_date) + '.csv'
		for item in os.listdir("Dependencies"):
			path = os.path.join(item)

			if (item.startswith('all_instrument')) and (current_date not in item.split(" ")[1]):
				if os.path.isfile("Dependencies\\" + path):
					os.remove("Dependencies\\" + path)

		if expected_file in os.listdir("Dependencies"):
			try:
				print(f"reading existing file {expected_file}")
				instrument_df = pd.read_csv("Dependencies\\" + expected_file, low_memory=False)
			except Exception as e:
				print(
					"This BOT Is Instrument file is not generated completely, Picking New File from Dhan Again")
				instrument_df = pd.read_csv("https://images.dhan.co/api-data/api-scrip-master.csv", low_memory=False)

		else:
			# this will fetch instrument_df file from Dhan
			print("This BOT Is Picking New File From Dhan")
			instrument_df = pd.read_csv("https://images.dhan.co/api-data/api-scrip-master.csv", low_memory=False)

		return instrument_df

instrument_df = get_instrument_file()


url = "https://api.dhan.co/charts/intraday"

client_code = "1105577608"
token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"


Dhan = dhanhq(client_code, token_id)
print("self.Dhan.FNO =", Dhan.FNO)

user_df = pd.read_csv('User_options_input.csv')

interval = '5'
exchange = 'NFO'


tradingsymbol = user_df.iloc[1]['DISPLAY_NAME']


script_exchange = {"NSE":Dhan.NSE, "NFO":Dhan.FNO, "BFO":"BSE_FNO", "CUR": Dhan.CUR, "BSE":Dhan.BSE, "MCX":Dhan.MCX, "INDEX":Dhan.INDEX}
instrument_exchange = {'NSE':"NSE",'BSE':"BSE",'NFO':'NSE','BFO':'BSE','MCX':'MCX','CUR':'NSE'}
exchange_segment = script_exchange[exchange]
index_exchange = {"NIFTY":'NSE',"BANKNIFTY":"NSE","FINNIFTY":"NSE","MIDCPNIFTY":"NSE","BANKEX":"BSE","SENSEX":"BSE"}
if tradingsymbol in index_exchange:
    exchange =index_exchange[tradingsymbol]

security_id = instrument_df[((instrument_df['SEM_TRADING_SYMBOL']==tradingsymbol)|(instrument_df['SEM_CUSTOM_SYMBOL']==tradingsymbol))&(instrument_df['SEM_EXM_EXCH_ID']==instrument_exchange[exchange])].iloc[-1]['SEM_SMST_SECURITY_ID']
instrument_type = instrument_df[((instrument_df['SEM_TRADING_SYMBOL']==tradingsymbol)|(instrument_df['SEM_CUSTOM_SYMBOL']==tradingsymbol))&(instrument_df['SEM_EXM_EXCH_ID']==instrument_exchange[exchange])].iloc[-1]['SEM_INSTRUMENT_NAME']

print("security_id", security_id)
print("instrument_type", instrument_type)
print('tradingsymbol', tradingsymbol)

test_ohlc = Dhan.intraday_minute_data(str(security_id),Dhan.FNO, instrument_type, '2025-05-16', '2025-06-20',int(interval))


df = pd.DataFrame(test_ohlc['data'])
df['timestamp'] = df['timestamp'].apply(lambda x: Dhan.convert_to_date_time(x))
print(df.head(10))
print(df.tail(10))


