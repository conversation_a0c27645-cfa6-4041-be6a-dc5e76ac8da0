#!/usr/bin/env python3
"""
Test script for the enhanced historical_fetcher.py
Tests both existing functionality and new option data fetching capabilities
"""

import os
import sys
import pandas as pd
import datetime

def test_imports():
    """Test that all required modules can be imported"""
    print("=== Testing Imports ===")
    try:
        import historical_fetcher as hf
        print("✓ historical_fetcher imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import historical_fetcher: {e}")
        return False

def test_option_infrastructure():
    """Test option data infrastructure functions"""
    print("\n=== Testing Option Infrastructure ===")
    
    try:
        import historical_fetcher as hf
        
        # Test instrument file function
        print("Testing get_instrument_file()...")
        try:
            instrument_df = hf.get_instrument_file()
            if instrument_df is not None:
                print(f"✓ Instrument file loaded: {len(instrument_df)} records")
            else:
                print("✗ Instrument file loading returned None")
        except Exception as e:
            print(f"✗ Error in get_instrument_file(): {e}")
        
        # Test client initialization
        print("Testing initialize_option_clients()...")
        try:
            dhan_client, tsl_client = hf.initialize_option_clients()
            if dhan_client is not None:
                print("✓ Dhan client initialized")
            else:
                print("✗ Dhan client initialization failed")
            
            if tsl_client is not None:
                print("✓ Tradehull client initialized")
            else:
                print("→ Tradehull client not available (expected if module not installed)")
        except Exception as e:
            print(f"✗ Error in initialize_option_clients(): {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing option infrastructure: {e}")
        return False

def test_csv_processing():
    """Test CSV processing functionality"""
    print("\n=== Testing CSV Processing ===")
    
    try:
        import historical_fetcher as hf
        
        # Test loading options CSV
        print("Testing load_options_input_csv()...")
        try:
            options_df = hf.load_options_input_csv()
            if options_df is not None:
                print(f"✓ Options CSV loaded: {len(options_df)} symbols")
                print(f"  Columns: {list(options_df.columns)}")
                print(f"  Sample symbols: {options_df['DISPLAY_NAME'].head(2).tolist()}")
            else:
                print("✗ Options CSV loading returned None")
        except Exception as e:
            print(f"✗ Error in load_options_input_csv(): {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing CSV processing: {e}")
        return False

def test_existing_functionality():
    """Test that existing historical data fetching still works"""
    print("\n=== Testing Existing Functionality ===")
    
    try:
        import historical_fetcher as hf
        
        # Test the original fetch_historical_data function
        print("Testing fetch_historical_data()...")
        try:
            # Test with a short interval to avoid long waits
            df, from_date, to_date = hf.fetch_historical_data("60")
            if df is not None and not df.empty:
                print(f"✓ Historical data fetched: {len(df)} records")
                print(f"  Date range: {from_date} to {to_date}")
                print(f"  Columns: {list(df.columns)}")
            else:
                print("✗ Historical data fetching returned empty result")
        except Exception as e:
            print(f"✗ Error in fetch_historical_data(): {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing existing functionality: {e}")
        return False

def test_file_structure():
    """Test that required files and directories exist"""
    print("\n=== Testing File Structure ===")
    
    # Check for required files
    required_files = [
        "historical_fetcher.py",
        "User_options_input.csv",
        "processing_functions.py"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")
    
    # Check CSV structure
    if os.path.exists("User_options_input.csv"):
        try:
            df = pd.read_csv("User_options_input.csv")
            required_columns = ['UNDERLYING_SYMBOL', 'DISPLAY_NAME']
            missing_cols = [col for col in required_columns if col not in df.columns]
            
            if not missing_cols:
                print(f"✓ CSV has required columns: {required_columns}")
                print(f"  Records: {len(df)}")
            else:
                print(f"✗ CSV missing columns: {missing_cols}")
        except Exception as e:
            print(f"✗ Error reading CSV: {e}")
    
    return True

def main():
    """Run all tests"""
    print("Enhanced Historical Fetcher Integration Test")
    print("=" * 50)
    
    tests = [
        test_file_structure,
        test_imports,
        test_option_infrastructure,
        test_csv_processing,
        test_existing_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} failed with exception: {e}")
    
    print(f"\n=== Test Summary ===")
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("✓ All tests passed! Enhanced historical_fetcher.py is ready to use.")
    else:
        print("✗ Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
