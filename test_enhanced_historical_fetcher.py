#!/usr/bin/env python3
"""
Test script for the enhanced historical_fetcher.py
Tests both existing functionality and new option data fetching capabilities
"""

import os
import sys
import pandas as pd
import datetime

def test_imports():
    """Test that all required modules can be imported"""
    print("=== Testing Imports ===")
    try:
        import historical_fetcher as hf
        print("✓ historical_fetcher imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import historical_fetcher: {e}")
        return False

def test_option_infrastructure():
    """Test option data infrastructure functions"""
    print("\n=== Testing Option Infrastructure ===")

    try:
        import historical_fetcher as hf

        # Test instrument file function
        print("Testing get_instrument_file()...")
        try:
            instrument_df = hf.get_instrument_file()
            if instrument_df is not None:
                print(f"✓ Instrument file loaded: {len(instrument_df)} records")
                print(f"  Sample columns: {list(instrument_df.columns)[:5]}")
            else:
                print("✗ Instrument file loading returned None")
        except Exception as e:
            print(f"✗ Error in get_instrument_file(): {e}")

        # Test security details function
        print("Testing get_option_security_details()...")
        try:
            # Test with a sample option symbol from the CSV
            test_symbol = "NIFTY 26 JUN 24500 PUT"
            security_id, instrument_type = hf.get_option_security_details(test_symbol, "NFO")
            if security_id is not None and instrument_type is not None:
                print(f"✓ Security details found for {test_symbol}")
                print(f"  Security ID: {security_id}, Instrument Type: {instrument_type}")
            else:
                print(f"✗ Could not find security details for {test_symbol}")
        except Exception as e:
            print(f"✗ Error in get_option_security_details(): {e}")

        return True

    except Exception as e:
        print(f"✗ Error testing option infrastructure: {e}")
        return False

def test_csv_processing():
    """Test CSV processing functionality"""
    print("\n=== Testing CSV Processing ===")

    try:
        import historical_fetcher as hf

        # Test CSV file existence and structure
        print("Testing options CSV file...")
        try:
            if os.path.exists(hf.options_csv_file):
                options_df = pd.read_csv(hf.options_csv_file)
                print(f"✓ Options CSV loaded: {len(options_df)} symbols")
                print(f"  Columns: {list(options_df.columns)}")
                if 'DISPLAY_NAME' in options_df.columns:
                    print(f"  Sample symbols: {options_df['DISPLAY_NAME'].head(2).tolist()}")
            else:
                print(f"✗ Options CSV file not found: {hf.options_csv_file}")
        except Exception as e:
            print(f"✗ Error reading options CSV: {e}")

        # Test process_options_csv function (dry run)
        print("Testing process_options_csv() function availability...")
        try:
            if hasattr(hf, 'process_options_csv'):
                print("✓ process_options_csv function is available")
            else:
                print("✗ process_options_csv function not found")
        except Exception as e:
            print(f"✗ Error checking process_options_csv: {e}")

        return True

    except Exception as e:
        print(f"✗ Error testing CSV processing: {e}")
        return False

def test_option_data_fetching():
    """Test option data fetching functionality"""
    print("\n=== Testing Option Data Fetching ===")

    try:
        import historical_fetcher as hf

        # Test fetch_option_data function availability
        print("Testing fetch_option_data() function availability...")
        if hasattr(hf, 'fetch_option_data'):
            print("✓ fetch_option_data function is available")
        else:
            print("✗ fetch_option_data function not found")
            return False

        # Test configuration variables
        print("Testing option configuration...")
        config_vars = ['options_output_dir', 'option_timeframes', 'options_csv_file']
        for var in config_vars:
            if hasattr(hf, var):
                value = getattr(hf, var)
                print(f"✓ {var}: {value}")
            else:
                print(f"✗ {var} not found")

        return True

    except Exception as e:
        print(f"✗ Error testing option data fetching: {e}")
        return False

def test_existing_functionality():
    """Test that existing historical data fetching still works"""
    print("\n=== Testing Existing Functionality ===")

    try:
        import historical_fetcher as hf

        # Test the original fetch_historical_data function
        print("Testing fetch_historical_data()...")
        try:
            # Test with a short interval to avoid long waits
            df, from_date, to_date = hf.fetch_historical_data("60")
            if df is not None and not df.empty:
                print(f"✓ Historical data fetched: {len(df)} records")
                print(f"  Date range: {from_date} to {to_date}")
                print(f"  Columns: {list(df.columns)}")
            else:
                print("✗ Historical data fetching returned empty result")
        except Exception as e:
            print(f"✗ Error in fetch_historical_data(): {e}")

        return True

    except Exception as e:
        print(f"✗ Error testing existing functionality: {e}")
        return False

def test_file_structure():
    """Test that required files and directories exist"""
    print("\n=== Testing File Structure ===")
    
    # Check for required files
    required_files = [
        "historical_fetcher.py",
        "User_options_input.csv",
        "processing_functions.py"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")
    
    # Check CSV structure
    if os.path.exists("User_options_input.csv"):
        try:
            df = pd.read_csv("User_options_input.csv")
            required_columns = ['UNDERLYING_SYMBOL', 'DISPLAY_NAME']
            missing_cols = [col for col in required_columns if col not in df.columns]
            
            if not missing_cols:
                print(f"✓ CSV has required columns: {required_columns}")
                print(f"  Records: {len(df)}")
            else:
                print(f"✗ CSV missing columns: {missing_cols}")
        except Exception as e:
            print(f"✗ Error reading CSV: {e}")
    
    return True

def main():
    """Run all tests"""
    print("Enhanced Historical Fetcher Integration Test")
    print("=" * 50)

    tests = [
        test_file_structure,
        test_imports,
        test_option_infrastructure,
        test_csv_processing,
        test_option_data_fetching,
        test_existing_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} failed with exception: {e}")
    
    print(f"\n=== Test Summary ===")
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("✓ All tests passed! Enhanced historical_fetcher.py is ready to use.")
    else:
        print("✗ Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
