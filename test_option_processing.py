#!/usr/bin/env python3
"""
Quick test of option data processing functionality
"""

import os
import sys
import pandas as pd

def test_option_processing():
    """Test the complete option processing workflow"""
    print("=== Testing Option Data Processing Workflow ===")
    
    try:
        import historical_fetcher as hf
        
        # Test the complete process_options_csv function
        print("Running process_options_csv()...")
        print("This will fetch option data for all entries in User_options_input.csv")
        print("across all timeframes (1min/10days, 5min/20days, 60min/60days)")
        print("-" * 60)
        
        results = hf.process_options_csv()
        
        print("\n=== Results Summary ===")
        if results["success"]:
            print(f"✓ Processing completed successfully")
            print(f"  Total entries: {results['total_entries']}")
            print(f"  Processed entries: {results['processed_entries']}")
            print(f"  Successful fetches: {results['successful_fetches']}")
            print(f"  Failed fetches: {results['failed_fetches']}")
            
            # Check output files
            if os.path.exists(hf.options_output_dir):
                files = os.listdir(hf.options_output_dir)
                print(f"\n✓ Output directory created: {hf.options_output_dir}")
                print(f"  Files created: {len(files)}")
                for file in files[:5]:  # Show first 5 files
                    print(f"    - {file}")
                if len(files) > 5:
                    print(f"    ... and {len(files) - 5} more files")
            
            # Show detailed results
            print(f"\n=== Detailed Results ===")
            for detail in results['details']:
                symbol = detail['symbol']
                print(f"\nSymbol: {symbol}")
                if 'timeframes' in detail:
                    for timeframe, tf_result in detail['timeframes'].items():
                        status = tf_result['status']
                        if status == 'success':
                            records = tf_result['records']
                            file = tf_result['file']
                            print(f"  {timeframe}min: ✓ {records} records -> {file}")
                        else:
                            error = tf_result.get('error', 'Unknown error')
                            print(f"  {timeframe}min: ✗ {error}")
                else:
                    print(f"  Status: {detail.get('status', 'Unknown')}")
                    if 'error' in detail:
                        print(f"  Error: {detail['error']}")
        else:
            print(f"✗ Processing failed: {results.get('error', 'Unknown error')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error during option processing test: {e}")
        return False

def main():
    """Run the option processing test"""
    print("Option Data Processing Test")
    print("=" * 50)
    
    success = test_option_processing()
    
    if success:
        print("\n✓ Option processing test completed successfully!")
    else:
        print("\n✗ Option processing test failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
