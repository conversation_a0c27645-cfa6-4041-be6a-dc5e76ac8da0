# historical_fetcher_v2.py
# Enhanced version with integrated option data fetching capabilities
# Fetch past 5 trading days of intraday data using DhanHQ 2.0 compatible API (with from_date/to_date)
# Added option data fetching for multiple timeframes from User_options_input.csv

import os
import datetime
import pandas as pd
import requests
import time
from dhanhq import dhanhq

# Optional import for Tradehull
# try:
#     from Dhan_Tradehull import Tradehull
#     TRADEHULL_AVAILABLE = True
# except ImportError:
#     Tradehull = None
#     TRADEHULL_AVAILABLE = False

from processing_functions import process_dataframe

# === Configuration ===
client_code = "**********"
token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

output_dir = "processed_files"
options_output_dir = "processed_options_files"

# === Option Data Configuration ===
# Exchange mappings for option data
script_exchange = {"NSE": None, "NFO": None, "BFO": "BSE_FNO", "CUR": None, "BSE": None, "MCX": None, "INDEX": None}
instrument_exchange = {'NSE': "NSE", 'BSE': "BSE", 'NFO': 'NSE', 'BFO': 'BSE', 'MCX': 'MCX', 'CUR': 'NSE'}
index_exchange = {"NIFTY": 'NSE', "BANKNIFTY": "NSE", "FINNIFTY": "NSE", "MIDCPNIFTY": "NSE", "BANKEX": "BSE", "SENSEX": "BSE"}

# Global variables for option data
instrument_df = None
dhan_client = None
tsl_client = None


def get_instrument_file():
    """
    Get instrument file for option data processing.
    Downloads fresh file from Dhan if needed or uses existing file.

    Returns:
        pd.DataFrame: Instrument data DataFrame
    """
    global instrument_df

    try:
        current_date = time.strftime("%Y-%m-%d")
        expected_file = 'all_instrument ' + str(current_date) + '.csv'
        dependencies_dir = "Dependencies"

        # Create Dependencies directory if it doesn't exist
        os.makedirs(dependencies_dir, exist_ok=True)

        # Clean up old instrument files
        for item in os.listdir(dependencies_dir):
            if (item.startswith('all_instrument')) and (current_date not in item.split(" ")[1]):
                file_path = os.path.join(dependencies_dir, item)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    #print(f"  → Removed old instrument file: {item}")

        # Check if current file exists
        expected_file_path = os.path.join(dependencies_dir, expected_file)
        if os.path.exists(expected_file_path):
            try:
                #print(f"  → Reading existing instrument file: {expected_file}")
                instrument_df = pd.read_csv(expected_file_path, low_memory=False)
                return instrument_df
            except Exception as e:
                #print(f"  ✗ Error reading existing file, downloading fresh: {str(e)}")
                pass

        # Download fresh instrument file from Dhan
        #print("  → Downloading fresh instrument file from Dhan")
        instrument_df = pd.read_csv("https://images.dhan.co/api-data/api-scrip-master.csv", low_memory=False)

        # Save the downloaded file
        instrument_df.to_csv(expected_file_path, index=False)
        #print(f"  ✓ Saved instrument file: {expected_file}")

        return instrument_df

    except Exception as e:
        print(f"  ✗ Error in get_instrument_file: {str(e)}")
        return None


def initialize_option_clients():
    """
    Initialize Dhan and Tradehull clients for option data fetching.

    Returns:
        tuple: (dhan_client, tsl_client) or (None, None) if initialization fails
    """
    global dhan_client, tsl_client, script_exchange

    try:
        # Initialize DhanHQ client
        dhan_client = dhanhq(client_code, token)

        # Initialize Tradehull client
        if TRADEHULL_AVAILABLE and Tradehull is not None:
            try:
                tsl_client = Tradehull(client_code, token)
            except Exception as e:
                tsl_client = None
                #print(f"  → Tradehull client initialization failed: {str(e)}")
        else:
            tsl_client = None
            #print("  → Tradehull not available, using Dhan only")

        # Update script_exchange with actual Dhan constants
        script_exchange.update({
            "NSE": dhan_client.NSE,
            "NFO": dhan_client.FNO,
            "CUR": dhan_client.CUR,
            "BSE": dhan_client.BSE,
            "MCX": dhan_client.MCX,
            "INDEX": dhan_client.INDEX
        })

        #print("  ✓ Option clients initialized successfully")
        return dhan_client, tsl_client

    except Exception as e:
        print(f"  ✗ Error initializing option clients: {str(e)}")
        return None, None


def fetch_option_historical_data(tradingsymbol, interval="5", exchange="NFO"):
    """
    Fetch historical option data for a specific trading symbol and interval.

    Args:
        tradingsymbol (str): Trading symbol (e.g., "NIFTY 26 JUN 24500 PUT")
        interval (str): Data interval ("1", "5", "60")
        exchange (str): Exchange name (default: "NFO")

    Returns:
        tuple: (DataFrame, from_date_str, to_date_str) or (None, None, None) if failed
    """
    global instrument_df, dhan_client

    if instrument_df is None or dhan_client is None:
        #print(f"  ✗ Instrument data or Dhan client not initialized")
        return None, None, None

    try:
        # Calculate date ranges based on interval (following existing patterns)
        now = datetime.datetime.now()
        today = now.date()
        current_time = now.time()
        market_start = datetime.time(9, 15)
        today_curr_date_time = f"{today} {current_time}"
        today_market_start = f"{today} {market_start}"

        if today_curr_date_time < today_market_start:
            end_date = today - datetime.timedelta(days=1)
            while end_date.weekday() > 4:  # Skip weekends
                end_date = end_date - datetime.timedelta(days=1)
        else:
            end_date = today

        # Calculate from_date based on interval (matching requirements)
        if interval == "1":
            from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d")
        elif interval == "5":
            from_date = (end_date - datetime.timedelta(days=20)).strftime("%Y-%m-%d")
        elif interval == "60":
            from_date = (end_date - datetime.timedelta(days=60)).strftime("%Y-%m-%d")
        else:
            raise ValueError(f"Invalid interval for options: {interval}")

        to_date = end_date.strftime("%Y-%m-%d")

        # Handle index exchange mapping
        if tradingsymbol in index_exchange:
            exchange = index_exchange[tradingsymbol]

        # Find security ID and instrument type from instrument data
        try:
            instrument_filter = (
                ((instrument_df['SEM_TRADING_SYMBOL'] == tradingsymbol) |
                 (instrument_df['SEM_CUSTOM_SYMBOL'] == tradingsymbol)) &
                (instrument_df['SEM_EXM_EXCH_ID'] == instrument_exchange[exchange])
            )

            matching_instruments = instrument_df[instrument_filter]
            if matching_instruments.empty:
                #print(f"  ✗ No instrument found for symbol: {tradingsymbol}")
                return None, None, None

            security_id = matching_instruments.iloc[-1]['SEM_SMST_SECURITY_ID']
            instrument_type = matching_instruments.iloc[-1]['SEM_INSTRUMENT_NAME']

        except Exception as e:
            #print(f"  ✗ Error finding instrument data for {tradingsymbol}: {str(e)}")
            return None, None, None

        # Fetch option data using Dhan API
        try:
            exchange_segment = script_exchange[exchange]

            # Use Dhan's intraday_minute_data method
            response = dhan_client.intraday_minute_data(
                str(security_id),
                exchange_segment,
                instrument_type,
                from_date,
                to_date,
                int(interval)
            )

            if response and 'data' in response:
                # Create DataFrame from response
                df = pd.DataFrame(response['data'])

                if not df.empty:
                    # Convert timestamp using Dhan's converter
                    df['timestamp'] = df['timestamp'].apply(lambda x: dhan_client.convert_to_date_time(x))

                    # Reorder columns to match existing pattern
                    df = df[["timestamp", "open", "high", "low", "close", "volume"]]

                    #print(f"  ✓ Fetched {len(df)} records for {tradingsymbol} ({interval}min)")
                    return df, from_date, to_date
                else:
                    #print(f"  → No data available for {tradingsymbol} ({interval}min)")
                    return None, from_date, to_date
            else:
                #print(f"  ✗ Invalid response for {tradingsymbol}: {response}")
                return None, from_date, to_date

        except Exception as e:
            #print(f"  ✗ Error fetching data for {tradingsymbol}: {str(e)}")
            return None, from_date, to_date

    except Exception as e:
        print(f"  ✗ Error in fetch_option_historical_data: {str(e)}")
        return None, None, None


def load_options_input_csv(csv_path="User_options_input.csv"):
    """
    Load and validate the User_options_input.csv file.

    Args:
        csv_path (str): Path to the CSV file

    Returns:
        pd.DataFrame: DataFrame with option symbols or None if failed
    """
    try:
        if not os.path.exists(csv_path):
            print(f"  ✗ Options input file not found: {csv_path}")
            return None

        # Read CSV file
        options_df = pd.read_csv(csv_path)

        # Validate required columns
        required_columns = ['UNDERLYING_SYMBOL', 'DISPLAY_NAME']
        missing_columns = [col for col in required_columns if col not in options_df.columns]

        if missing_columns:
            print(f"  ✗ Missing required columns in {csv_path}: {missing_columns}")
            return None

        # Remove empty rows
        options_df = options_df.dropna(subset=['DISPLAY_NAME'])

        if options_df.empty:
            print(f"  ✗ No valid option symbols found in {csv_path}")
            return None

        #print(f"  ✓ Loaded {len(options_df)} option symbols from {csv_path}")
        return options_df

    except Exception as e:
        print(f"  ✗ Error loading options CSV: {str(e)}")
        return None


def process_all_option_symbols(options_df, intervals=["1", "5", "60"]):
    """
    Process all option symbols from the CSV for specified intervals.

    Args:
        options_df (pd.DataFrame): DataFrame with option symbols
        intervals (list): List of intervals to fetch ("1", "5", "60")

    Returns:
        dict: Dictionary with processed data {symbol: {interval: processed_df}}
    """
    if options_df is None or options_df.empty:
        #print("  ✗ No option symbols to process")
        return {}

    processed_options = {}
    total_symbols = len(options_df)

    #print(f"  → Processing {total_symbols} option symbols for intervals: {intervals}")

    for idx, row in options_df.iterrows():
        symbol = row['DISPLAY_NAME']
        underlying = row['UNDERLYING_SYMBOL']

        #print(f"  → Processing symbol {idx+1}/{total_symbols}: {symbol}")

        symbol_data = {}

        for interval in intervals:
            try:
                # Fetch option data
                df, from_date, to_date = fetch_option_historical_data(symbol, interval)

                if df is not None and not df.empty:
                    # Process the data using existing processing function
                    processed_df = process_dataframe(df)

                    if processed_df is not None:
                        symbol_data[interval] = {
                            'data': processed_df,
                            'from_date': from_date,
                            'to_date': to_date,
                            'underlying': underlying
                        }

                        # Save processed data to file
                        save_option_data(symbol, interval, processed_df, from_date, to_date)

                        #print(f"    ✓ Processed {interval}min data: {len(processed_df)} records")
                    else:
                        #print(f"    ✗ Failed to process {interval}min data")
                        pass
                else:
                    #print(f"    → No {interval}min data available")
                    pass

            except Exception as e:
                print(f"    ✗ Error processing {symbol} ({interval}min): {str(e)}")

        if symbol_data:
            processed_options[symbol] = symbol_data

    #print(f"  ✓ Completed processing {len(processed_options)} option symbols")
    return processed_options


def save_option_data(symbol, interval, df, from_date, to_date):
    """
    Save processed option data to CSV file.

    Args:
        symbol (str): Trading symbol
        interval (str): Data interval
        df (pd.DataFrame): Processed data
        from_date (str): Start date
        to_date (str): End date
    """
    try:
        # Create output directory if it doesn't exist
        os.makedirs(options_output_dir, exist_ok=True)

        # Create safe filename from symbol (replace spaces and special chars)
        safe_symbol = symbol.replace(" ", "_").replace("/", "_").replace(":", "_")

        # Create filename following existing pattern
        filename = f"{safe_symbol}_{interval}min_{from_date}_to_{to_date}_processed.csv"
        output_path = os.path.join(options_output_dir, filename)

        # Save to CSV
        df.to_csv(output_path, index=False)
        #print(f"    ✓ Saved: {filename}")

    except Exception as e:
        print(f"    ✗ Error saving option data: {str(e)}")

def fetch_historical_data(interval="60"):
    """
    Fetch historical data from Dhan API based on interval
    Intervals:
    - "1": 1 minute (5 days)
    - "5": 5 minutes (10 days)
    - "60": 60 minutes (30 days)
    - "daily": Daily data (1 year)
    """
    security_id = "13"  # NIFTY
    exchange_segment = "IDX_I"
    instrument_type = "INDEX"
    
    now = datetime.datetime.now()
    today = now.date()
    current_time = now.time()
    market_start = datetime.time(9, 15)
    today_curr_date_time = f"{today} {current_time}"
    today_market_start = f"{today} {market_start}"

    # #print("today_curr_date_time: ",today_curr_date_time)
    # #print("today_market_start: ",today_market_start)
    
    if today_curr_date_time < today_market_start:
        end_date = today - datetime.timedelta(days=1)
        while end_date.weekday() > 4:  # 5 is Saturday, 6 is Sunday
            end_date = end_date - datetime.timedelta(days=1)
    else:
        end_date = today
    
    ##print("End date: ",end_date)

    # Calculate from_date based on interval
    if interval == "1":
        from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "5":
        from_date = (end_date - datetime.timedelta(days=20)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "60":
        from_date = (end_date - datetime.timedelta(days=60)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "daily":
        from_date = (end_date - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
    else:
        raise ValueError(f"Invalid interval: {interval}")
    
    # For intraday data, always use 15:29 as the end time
    to_date = end_date.strftime("%Y-%m-%d 15:29:00") if interval != "daily" else end_date.strftime("%Y-%m-%d")

    #print("To date validate: ",to_date)

    # === Request Setup ===
    headers = {
        "access-token": token,
        "Content-Type": "application/json"
    }

    if interval == "daily":
        # Initialize DhanHQ client for daily data
        client = dhanhq("**********", token)
        res = client.historical_daily_data(
            security_id=security_id,
            exchange_segment=exchange_segment,
            instrument_type=instrument_type,
            expiry_code=0,
            from_date=from_date,
            to_date=to_date
        )
        data = res
    else:
        # Setup for intraday data
        payload = {
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "instrument": instrument_type,
            "interval": interval,
            "oi": False,
            "fromDate": from_date,
            "toDate": to_date
        }
        url = "https://api.dhan.co/v2/charts/intraday"
        resp = requests.post(url, json=payload, headers=headers)
        data = resp.json()

    # #print the response to understand the structure
    #print("API Response:")
    # #print(data)
    #print("\nResponse keys:", list(data.keys()) if isinstance(data, dict) else "Not a dictionary")

    # Check if the response is successful and has the expected structure
    if isinstance(data, dict):
        if "data" in data and interval != "daily":
            chart_data = data["data"]
            #print("\nChart data keys:", list(chart_data.keys()) if isinstance(chart_data, dict) else "Chart data is not a dictionary")
            
            # Create DataFrame from individual lists
            df = pd.DataFrame({
                "open": chart_data["open"],
                "high": chart_data["high"],
                "low": chart_data["low"],
                "close": chart_data["close"],
                "volume": chart_data["volume"],
                "timestamp": pd.to_datetime(chart_data["timestamp"], unit="s", utc=True)
            })
        elif "data" in data and interval == "daily":
            # Handle daily data format
            df = pd.DataFrame(data["data"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
        elif all(key in data for key in ["open", "high", "low", "close", "volume", "timestamp"]):
            # Data is directly in the response
            df = pd.DataFrame({
                "open": data["open"],
                "high": data["high"],
                "low": data["low"],
                "close": data["close"],
                "volume": data["volume"],
                "timestamp": pd.to_datetime(data["timestamp"], unit="s", utc=True)
            })
        else:
            #print("❌ Unexpected response structure. Cannot create DataFrame.")
            return None

    # Convert timestamp to IST (UTC+5:30)
    IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
    # Convert to IST and overwrite 'timestamp'
    df["timestamp"] = df["timestamp"].dt.tz_convert(IST)

    # Filter out data points after 15:29 for intraday data
    if interval != "daily":
        df = df[df["timestamp"].dt.time <= datetime.time(15, 29)]

    # Reorder columns: IST timestamp first
    df = df[["timestamp", "open", "high", "low", "close", "volume"]]

    # Create filename based on interval
    # interval_suffix = "daily" if interval == "daily" else f"{interval}min"
    # filename = f"NIFTY_INDEX_{interval_suffix}_{from_date[:10]}_to_{to_date[:10]}.csv"
    
    # # Save to CSV
    # output_path = os.path.join(output_dir, filename)
    # df.to_csv(output_path, index=False)
    # df.to_csv(filename, index=False)
    
    # Output preview
    #print("✅ Data fetched from {} to {}".format(from_date, to_date))
    # print(df.head(10))
    # print(df.tail(10))
    
    return df, from_date[:10], to_date[:10]


# Example usage
if __name__ == "__main__":
    fetch_interval_seconds = 10 # Define the interval in seconds
    now = datetime.datetime.now()
    current_time = now.time()
    market_close = datetime.time(15, 29)
    market_start = datetime.time(9, 15)

    # Create output directories if they don't exist
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(options_output_dir, exist_ok=True)

    # Initialize option data infrastructure
    print("=== Initializing Option Data Infrastructure ===")
    try:
        instrument_df = get_instrument_file()
        if instrument_df is not None:
            print(f"✓ Instrument file loaded with {len(instrument_df)} instruments")
        else:
            print("✗ Failed to load instrument file")
    except Exception as e:
        print(f"✗ Error loading instrument file: {str(e)}")
        instrument_df = None

    try:
        dhan_client, tsl_client = initialize_option_clients()
        if dhan_client is not None:
            print("✓ Dhan client initialized successfully")
        else:
            print("✗ Failed to initialize Dhan client")
    except Exception as e:
        print(f"✗ Error initializing clients: {str(e)}")
        dhan_client, tsl_client = None, None

    # Load options input CSV
    try:
        options_df = load_options_input_csv()
        if options_df is not None:
            print(f"✓ Options CSV loaded with {len(options_df)} symbols")
        else:
            print("✗ No valid options found in CSV")
    except Exception as e:
        print(f"✗ Error loading options CSV: {str(e)}")
        options_df = None

    options_processed = False  # Flag to track if options have been processed

    # Historical data variables
    data_store = {}  # In-memory store for dataframes: {interval: DataFrame}
    first_run = True
    last_15_min_fetch_minute = -1
    df_1 = None
    df_5 = None
    df_60 = None
    df_daily = None

    print("=== Starting Main Data Fetching Loop ===")
    print(f"Options to process: {len(options_df) if options_df is not None else 0} symbols")
    
    while True:
        now = datetime.datetime.now()
        current_time = now.time()
        # Fetch data for each interval
        # intervals = ["1", "5", "60", "daily"]
        intervals_to_fetch = set()
        if first_run:
            #print("--- First run: scheduling all intervals for full historical fetch. ---")
            intervals_to_fetch.update(["1", "5", "60", "daily"])
            first_run = False
        else:
            intervals_to_fetch.update(["1", "5"])

        if now.minute % 15 == 0 and now.minute != last_15_min_fetch_minute:
            #print(f"--- Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch. ---")
            intervals_to_fetch.add("60")
            last_15_min_fetch_minute = now.minute

        # # --- Fetch, Append, Process, and Save Data ---
        # for interval in sorted(list(intervals_to_fetch)):
        #     print(f"\n--- Processing interval: {interval} ---")

        

        #print("intervals to fetch: before for loop :",intervals_to_fetch)

        # === Historical Data Fetching (Existing Functionality) ===
        for interval in intervals_to_fetch:
            #print(f"\nFetching {interval} data...")
            df, from_date_str, to_date_str = fetch_historical_data(interval)

            # 3. Update Data Store
            if df is not None and not df.empty:
                #print(f"  Fetched {len(df)} new records.")
                if interval in data_store:
                    data_store[interval] = pd.concat([data_store[interval], df]).drop_duplicates(subset=['timestamp'], keep='last').sort_values(by='timestamp').reset_index(drop=True)
                else:
                    data_store[interval] = df.sort_values(by='timestamp').reset_index(drop=True)
                #print(f"  Data store for '{interval}' now has {len(data_store[interval])} total records.")

            if interval in data_store and not data_store[interval].empty:
                df_processed = process_dataframe(df)

            try:
                interval_suffix = "daily" if interval == "daily" else f"{interval}min"
                output_filename = f"NIFTY_INDEX_{interval_suffix}_{from_date_str}_to_{to_date_str}_processed.csv"
                output_path = os.path.join(output_dir, output_filename)

                df_processed.to_csv(output_path, index=False)
                #print(f"  ✓ Saved processed file: {output_path}")
                # successful_workflows += 1
            except Exception as e:
                print(f"  ✗ Error saving processed data for interval {interval}: {str(e)}")
                # cnt=1
                # failed_workflows += 1

            if df is not None:
                #print(f"✅ Successfully fetched {interval} data with {len(df)} rows")
                if interval == "1":
                    df_1 = df_processed
                elif interval == "5":
                    df_5 = df_processed
                elif interval == "60":
                    df_60 = df_processed
                else:
                    df_daily = df_processed

        # === Option Data Fetching (New Functionality) ===
        # Process options on first run or when market is open
        if first_run and options_df is not None and not options_processed:
            if instrument_df is not None and dhan_client is not None:
                print("\n=== Processing Option Data ===")
                try:
                    # Process all option symbols for all required timeframes
                    option_intervals = ["1", "5", "60"]  # 1min/10days, 5min/20days, 60min/60days
                    processed_options = process_all_option_symbols(options_df, option_intervals)

                    if processed_options:
                        print(f"✓ Successfully processed option data for {len(processed_options)} symbols")
                        options_processed = True
                    else:
                        print("✗ No option data was processed")

                except Exception as e:
                    print(f"✗ Error processing option data: {str(e)}")
            else:
                print("✗ Option infrastructure not properly initialized")
                if instrument_df is None:
                    print("  - Instrument data not available")
                if dhan_client is None:
                    print("  - Dhan client not initialized")

        # === Market Hours Check ===
        if (current_time < market_start) | (current_time > market_close):
            #print("Market is closed or time is past market close. Exiting...")
            break

        # === Display Current Data (Existing Functionality) ===
        # decissionmaking(df_1, df_5, df_60)
        print("dataframe for 1 min: ",df_1.tail(2))
        print("dataframe for 5 min: ",df_5.tail(2))
        print("dataframe for 60 min: ",df_60.tail(2))
        print("\n***********\n")
        # #print("dataframe for daily: ",df_daily.tail(2))

        time.sleep(fetch_interval_seconds) # Wait for the specified interval

    print("=== Data Fetching Session Completed ===")
    if options_processed:
        print(f"✓ Option data processing completed for all symbols")
        print(f"✓ Files saved to: {options_output_dir}")
    print(f"✓ Historical data files saved to: {output_dir}")
