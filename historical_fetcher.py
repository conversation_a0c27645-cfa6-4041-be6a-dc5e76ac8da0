# historical_fetcher_v2.py
# Enhanced version with integrated option data fetching capabilities
# Fetch past 5 trading days of intraday data using DhanHQ 2.0 compatible API (with from_date/to_date)
# Added option data fetching for multiple timeframes from User_options_input.csv

import os
import datetime
import pandas as pd
import requests
import time
from dhanhq import dhanhq

# Optional import for Tradehull
# try:
#     from Dhan_Tradehull import Tradehull
#     TRADEHULL_AVAILABLE = True
# except ImportError:
#     Tradehull = None
#     TRADEHULL_AVAILABLE = False

from processing_functions import process_dataframe

# === Configuration ===
token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"
client_code = "1105577608"

# Output directories
output_dir = "processed_files"
options_output_dir = "processed_options_files"

# Option data configuration
options_csv_file = "User_options_input.csv"
option_timeframes = ["1", "5", "60"]  # 1min/10days, 5min/20days, 60min/60days

# Global variable for instrument data
instrument_df = None


# === Option Data Functions ===

def get_instrument_file():
    """
    Get instrument file from Dhan API or local cache.
    Follows the same pattern as Option_fetch.py but adapted for historical_fetcher.py structure.

    Returns:
        pd.DataFrame: Instrument data DataFrame
    """
    global instrument_df

    try:
        current_date = time.strftime("%Y-%m-%d")
        expected_file = f'all_instrument {current_date}.csv'
        dependencies_dir = "Dependencies"

        # Create Dependencies directory if it doesn't exist
        os.makedirs(dependencies_dir, exist_ok=True)

        # Clean up old instrument files
        if os.path.exists(dependencies_dir):
            for item in os.listdir(dependencies_dir):
                if item.startswith('all_instrument') and current_date not in item:
                    file_path = os.path.join(dependencies_dir, item)
                    if os.path.isfile(file_path):
                        try:
                            os.remove(file_path)
                            #print(f"  ✓ Removed old instrument file: {item}")
                        except Exception as e:
                            print(f"  ✗ Error removing old file {item}: {str(e)}")

        # Check if current date file exists
        expected_file_path = os.path.join(dependencies_dir, expected_file)
        if os.path.exists(expected_file_path):
            try:
                #print(f"  → Reading existing instrument file: {expected_file}")
                instrument_df = pd.read_csv(expected_file_path, low_memory=False)
                return instrument_df
            except Exception as e:
                print(f"  ✗ Error reading existing instrument file: {str(e)}")
                # Fall through to download new file

        # Download new instrument file from Dhan
        #print("  → Downloading new instrument file from Dhan API")
        instrument_df = pd.read_csv("https://images.dhan.co/api-data/api-scrip-master.csv", low_memory=False)

        # Save the downloaded file for future use
        try:
            instrument_df.to_csv(expected_file_path, index=False)
            #print(f"  ✓ Saved instrument file: {expected_file}")
        except Exception as e:
            print(f"  ✗ Error saving instrument file: {str(e)}")

        return instrument_df

    except Exception as e:
        print(f"  ✗ Error in get_instrument_file: {str(e)}")
        return None


def get_option_security_details(tradingsymbol, exchange):
    """
    Get security details for option symbols from instrument data.

    Args:
        tradingsymbol (str): Trading symbol (e.g., "NIFTY 26 JUN 24500 PUT")
        exchange (str): Exchange (e.g., "NFO")

    Returns:
        tuple: (security_id, instrument_type) or (None, None) if not found
    """
    global instrument_df

    try:
        if instrument_df is None:
            instrument_df = get_instrument_file()

        if instrument_df is None:
            print(f"  ✗ Instrument data not available for {tradingsymbol}")
            return None, None

        # Exchange mapping
        instrument_exchange = {
            'NSE': 'NSE', 'BSE': 'BSE', 'NFO': 'NSE',
            'BFO': 'BSE', 'MCX': 'MCX', 'CUR': 'NSE'
        }

        # Index exchange mapping
        index_exchange = {
            "NIFTY": 'NSE', "BANKNIFTY": "NSE", "FINNIFTY": "NSE",
            "MIDCPNIFTY": "NSE", "BANKEX": "BSE", "SENSEX": "BSE"
        }

        # Adjust exchange for index symbols
        if tradingsymbol in index_exchange:
            exchange = index_exchange[tradingsymbol]

        # Filter instrument data
        filtered_df = instrument_df[
            ((instrument_df['SEM_TRADING_SYMBOL'] == tradingsymbol) |
             (instrument_df['SEM_CUSTOM_SYMBOL'] == tradingsymbol)) &
            (instrument_df['SEM_EXM_EXCH_ID'] == instrument_exchange[exchange])
        ]

        if filtered_df.empty:
            print(f"  ✗ No instrument found for {tradingsymbol} on {exchange}")
            return None, None

        # Get the last matching record
        last_record = filtered_df.iloc[-1]
        security_id = last_record['SEM_SMST_SECURITY_ID']
        instrument_type = last_record['SEM_INSTRUMENT_NAME']

        #print(f"  ✓ Found {tradingsymbol}: security_id={security_id}, instrument_type={instrument_type}")
        return security_id, instrument_type

    except Exception as e:
        print(f"  ✗ Error getting security details for {tradingsymbol}: {str(e)}")
        return None, None


def fetch_historical_data(interval="60"):
    """
    Fetch historical data from Dhan API based on interval
    Intervals:
    - "1": 1 minute (5 days)
    - "5": 5 minutes (10 days)
    - "60": 60 minutes (30 days)
    - "daily": Daily data (1 year)
    """
    security_id = "13"  # NIFTY
    exchange_segment = "IDX_I"
    instrument_type = "INDEX"
    
    now = datetime.datetime.now()
    today = now.date()
    current_time = now.time()
    market_start = datetime.time(9, 15)
    today_curr_date_time = f"{today} {current_time}"
    today_market_start = f"{today} {market_start}"

    # #print("today_curr_date_time: ",today_curr_date_time)
    # #print("today_market_start: ",today_market_start)
    
    if today_curr_date_time < today_market_start:
        end_date = today - datetime.timedelta(days=1)
        while end_date.weekday() > 4:  # 5 is Saturday, 6 is Sunday
            end_date = end_date - datetime.timedelta(days=1)
    else:
        end_date = today
    
    ##print("End date: ",end_date)

    # Calculate from_date based on interval
    if interval == "1":
        from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "5":
        from_date = (end_date - datetime.timedelta(days=20)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "60":
        from_date = (end_date - datetime.timedelta(days=60)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "daily":
        from_date = (end_date - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
    else:
        raise ValueError(f"Invalid interval: {interval}")
    
    # For intraday data, always use 15:29 as the end time
    to_date = end_date.strftime("%Y-%m-%d 15:29:00") if interval != "daily" else end_date.strftime("%Y-%m-%d")

    #print("To date validate: ",to_date)

    # === Request Setup ===
    headers = {
        "access-token": token,
        "Content-Type": "application/json"
    }

    if interval == "daily":
        # Initialize DhanHQ client for daily data
        client = dhanhq("1105577608", token)
        res = client.historical_daily_data(
            security_id=security_id,
            exchange_segment=exchange_segment,
            instrument_type=instrument_type,
            expiry_code=0,
            from_date=from_date,
            to_date=to_date
        )
        data = res
    else:
        # Setup for intraday data
        payload = {
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "instrument": instrument_type,
            "interval": interval,
            "oi": False,
            "fromDate": from_date,
            "toDate": to_date
        }
        url = "https://api.dhan.co/v2/charts/intraday"
        resp = requests.post(url, json=payload, headers=headers)
        data = resp.json()

    # #print the response to understand the structure
    #print("API Response:")
    # #print(data)
    #print("\nResponse keys:", list(data.keys()) if isinstance(data, dict) else "Not a dictionary")

    # Check if the response is successful and has the expected structure
    if isinstance(data, dict):
        if "data" in data and interval != "daily":
            chart_data = data["data"]
            #print("\nChart data keys:", list(chart_data.keys()) if isinstance(chart_data, dict) else "Chart data is not a dictionary")
            
            # Create DataFrame from individual lists
            df = pd.DataFrame({
                "open": chart_data["open"],
                "high": chart_data["high"],
                "low": chart_data["low"],
                "close": chart_data["close"],
                "volume": chart_data["volume"],
                "timestamp": pd.to_datetime(chart_data["timestamp"], unit="s", utc=True)
            })
        elif "data" in data and interval == "daily":
            # Handle daily data format
            df = pd.DataFrame(data["data"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
        elif all(key in data for key in ["open", "high", "low", "close", "volume", "timestamp"]):
            # Data is directly in the response
            df = pd.DataFrame({
                "open": data["open"],
                "high": data["high"],
                "low": data["low"],
                "close": data["close"],
                "volume": data["volume"],
                "timestamp": pd.to_datetime(data["timestamp"], unit="s", utc=True)
            })
        else:
            #print("❌ Unexpected response structure. Cannot create DataFrame.")
            return None

    # Convert timestamp to IST (UTC+5:30)
    IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
    # Convert to IST and overwrite 'timestamp'
    df["timestamp"] = df["timestamp"].dt.tz_convert(IST)

    # Filter out data points after 15:29 for intraday data
    if interval != "daily":
        df = df[df["timestamp"].dt.time <= datetime.time(15, 29)]

    # Reorder columns: IST timestamp first
    df = df[["timestamp", "open", "high", "low", "close", "volume"]]

    # Create filename based on interval
    # interval_suffix = "daily" if interval == "daily" else f"{interval}min"
    # filename = f"NIFTY_INDEX_{interval_suffix}_{from_date[:10]}_to_{to_date[:10]}.csv"
    
    # # Save to CSV
    # output_path = os.path.join(output_dir, filename)
    # df.to_csv(output_path, index=False)
    # df.to_csv(filename, index=False)
    
    # Output preview
    #print("✅ Data fetched from {} to {}".format(from_date, to_date))
    # print(df.head(10))
    # print(df.tail(10))
    
    return df, from_date[:10], to_date[:10]


def fetch_option_data(security_id, exchange_segment, instrument_type, interval, tradingsymbol):
    """
    Fetch option data from Dhan API for a specific security and interval.
    Follows the same patterns as fetch_historical_data but for options.

    Args:
        security_id (str): Security ID from instrument data
        exchange_segment (str): Exchange segment (e.g., Dhan.FNO)
        instrument_type (str): Instrument type from instrument data
        interval (str): Time interval ("1", "5", "60")
        tradingsymbol (str): Trading symbol for logging/naming

    Returns:
        tuple: (DataFrame, from_date_str, to_date_str) or (None, None, None) if error
    """
    try:
        # Calculate date ranges based on interval (following the requirements)
        now = datetime.datetime.now()
        today = now.date()
        current_time = now.time()
        market_start = datetime.time(9, 15)
        today_curr_date_time = f"{today} {current_time}"
        today_market_start = f"{today} {market_start}"

        # Determine end date
        if today_curr_date_time < today_market_start:
            end_date = today - datetime.timedelta(days=1)
            while end_date.weekday() > 4:  # Skip weekends
                end_date = end_date - datetime.timedelta(days=1)
        else:
            end_date = today

        # Calculate from_date based on interval requirements
        if interval == "1":
            # 1-minute intervals for 10 days
            from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d")
        elif interval == "5":
            # 5-minute intervals for 20 days
            from_date = (end_date - datetime.timedelta(days=20)).strftime("%Y-%m-%d")
        elif interval == "60":
            # 60-minute intervals for 60 days
            from_date = (end_date - datetime.timedelta(days=60)).strftime("%Y-%m-%d")
        else:
            raise ValueError(f"Invalid interval for options: {interval}")

        to_date = end_date.strftime("%Y-%m-%d")

        #print(f"  → Fetching option data for {tradingsymbol}: {from_date} to {to_date}, interval: {interval}")

        # Initialize Dhan client
        client = dhanhq(client_code, token)

        # Fetch option data using intraday_minute_data
        response = client.intraday_minute_data(
            str(security_id),
            exchange_segment,
            instrument_type,
            from_date,
            to_date,
            int(interval)
        )

        if not response or 'data' not in response:
            print(f"  ✗ No data received for {tradingsymbol}")
            return None, None, None

        # Create DataFrame from response
        df = pd.DataFrame(response['data'])

        if df.empty:
            print(f"  ✗ Empty data received for {tradingsymbol}")
            return None, None, None

        # Convert timestamp using Dhan's convert_to_date_time method
        df['timestamp'] = df['timestamp'].apply(lambda x: client.convert_to_date_time(x))

        # Convert to pandas datetime if not already
        if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
            df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Ensure IST timezone
        if df['timestamp'].dt.tz is None:
            # Assume the data is already in IST
            IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
            df['timestamp'] = df['timestamp'].dt.tz_localize(IST)

        # Filter out data points after 15:29 for intraday data
        df = df[df['timestamp'].dt.time <= datetime.time(15, 29)]

        # Reorder columns to match historical data format
        expected_columns = ["timestamp", "open", "high", "low", "close", "volume"]
        available_columns = [col for col in expected_columns if col in df.columns]
        df = df[available_columns]
        print("Option name: ",tradingsymbol, "Interval: ",interval)
        print(df.head(5))
        print(df.tail(5))

        #print(f"  ✓ Fetched {len(df)} records for {tradingsymbol}")
        return df, from_date, to_date

    except Exception as e:
        print(f"  ✗ Error fetching option data for {tradingsymbol}: {str(e)}")
        return None, None, None


def process_options_csv():
    """
    Process all entries in User_options_input.csv and fetch option data for all timeframes.
    Follows the established patterns for data processing and file management.

    Returns:
        dict: Dictionary with results summary
    """
    try:
        # Check if options CSV file exists
        if not os.path.exists(options_csv_file):
            print(f"  ✗ Options CSV file not found: {options_csv_file}")
            return {"success": False, "error": "CSV file not found"}

        # Read options CSV
        #print(f"  → Reading options input file: {options_csv_file}")
        user_df = pd.read_csv(options_csv_file)

        if user_df.empty:
            print(f"  ✗ Options CSV file is empty")
            return {"success": False, "error": "CSV file is empty"}

        # Create options output directory
        os.makedirs(options_output_dir, exist_ok=True)

        # Initialize instrument data
        global instrument_df
        if instrument_df is None:
            instrument_df = get_instrument_file()

        # Exchange mappings (from Option_fetch.py)
        script_exchange = {
            "NSE": "NSE", "NFO": "FNO", "BFO": "BSE_FNO",
            "CUR": "CUR", "BSE": "BSE", "MCX": "MCX", "INDEX": "INDEX"
        }

        results = {
            "success": True,
            "total_entries": len(user_df),
            "processed_entries": 0,
            "successful_fetches": 0,
            "failed_fetches": 0,
            "details": []
        }

        # Process each entry in the CSV
        for index, row in user_df.iterrows():
            try:
                underlying_symbol = row['UNDERLYING_SYMBOL']
                display_name = row['DISPLAY_NAME']

                #print(f"\n  → Processing entry {index + 1}/{len(user_df)}: {display_name}")

                # Determine exchange (default to NFO for options)
                exchange = 'NFO'

                # Get security details
                security_id, instrument_type = get_option_security_details(display_name, exchange)

                if security_id is None or instrument_type is None:
                    print(f"  ✗ Could not find security details for {display_name}")
                    results["failed_fetches"] += 1
                    results["details"].append({
                        "symbol": display_name,
                        "status": "failed",
                        "error": "Security details not found"
                    })
                    continue

                # Get exchange segment for API call
                client = dhanhq(client_code, token)
                exchange_segment = client.FNO  # Use FNO for options

                entry_results = {"symbol": display_name, "timeframes": {}}
                flag = 0
                options_df= {}
                options_df

                # Fetch data for each timeframe
                for interval in option_timeframes:
                    try:
                        df, from_date, to_date = fetch_option_data(
                            security_id, exchange_segment, instrument_type, interval, display_name
                        )
                        

                         
                        #df_interval,display_name

                        if df is not None and not df.empty:
                            # Process the dataframe using existing processing functions
                            df_processed = process_dataframe(df)
                            options_df[display_name][df_f'interval'] = df_processed
            

                            # Create filename following existing patterns
                            clean_symbol = display_name.replace(" ", "_").replace("/", "_")
                            interval_suffix = f"{interval}min"
                            output_filename = f"{clean_symbol}_{interval_suffix}_{from_date}_to_{to_date}_processed.csv"
                            output_path = os.path.join(options_output_dir, output_filename)

                            # Save processed data
                            df_processed.to_csv(output_path, index=False)
                            #print(f"    ✓ Saved {interval}min data: {output_filename}")

                            entry_results["timeframes"][interval] = {
                                "status": "success",
                                "records": len(df_processed),
                                "file": output_filename
                            }
                            results["successful_fetches"] += 1
                        else:
                            print(f"    ✗ No data for {interval}min timeframe")
                            entry_results["timeframes"][interval] = {
                                "status": "failed",
                                "error": "No data received"
                            }
                            results["failed_fetches"] += 1

                    except Exception as e:
                        print(f"    ✗ Error processing {interval}min timeframe: {str(e)}")
                        entry_results["timeframes"][interval] = {
                            "status": "failed",
                            "error": str(e)
                        }
                        results["failed_fetches"] += 1

                entry_results["status"] = "processed"
                results["details"].append(entry_results)
                results["processed_entries"] += 1

            except Exception as e:
                print(f"  ✗ Error processing entry {index + 1}: {str(e)}")
                results["failed_fetches"] += 1
                results["details"].append({
                    "symbol": row.get('DISPLAY_NAME', f'Entry {index + 1}'),
                    "status": "failed",
                    "error": str(e)
                })

        #print(f"\n  ✓ Options processing complete: {results['successful_fetches']} successful, {results['failed_fetches']} failed")
        return results

    except Exception as e:
        print(f"  ✗ Error in process_options_csv: {str(e)}")
        return {"success": False, "error": str(e)}

def find_latest_signals(df):
    """
    Analyze a processed option DataFrame to identify the most recent trading signals.

    1. Finds the most recent crossover signal ('upward' or 'downward') in the 'crossover_signal' column.
    2. Finds the most recent supply/demand zone ('Supply' or 'Demand') in the 'Zones_sup_dem' column.

    Args:
        df (pd.DataFrame): Processed DataFrame containing at least the columns:
            - 'timestamp'
            - 'crossover_signal'
            - 'Zones_sup_dem'
            - 'Zone_Status'

    Returns:
        dict: {
            'latest_crossover': {
                'timestamp': ...,
                'signal': ...,
                'row_index': ...
            } or None,
            'latest_zone': {
                'timestamp': ...,
                'zone_type': ...,
                'zone_status': ...,
                'row_index': ...
            } or None
        }
        If no signal/zone is found, the corresponding value is None.
    """
    result = {
        'latest_crossover': None,
        'latest_zone': None
    }

    # Check for empty DataFrame
    if df is None or df.empty:
        return result

    # Check for required columns
    required_cols = ['timestamp', 'crossover_signal', 'Zones_sup_dem', 'Zone_Status']
    for col in required_cols:
        if col not in df.columns:
            # Return early if any required column is missing
            return result

    # --- Find most recent crossover signal ---
    try:
        mask_crossover = df['crossover_signal'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            # result['latest_crossover'] = {
            #     'timestamp': df.at[last_idx, 'timestamp'],
            #     'signal': df.at[last_idx, 'crossover_signal'],
            #     'row_index': last_idx
            # }
            result['latest_crossover'] = df.iloc[last_idx][:]
    except Exception as e:
        # If any error, leave as None
        pass

    # --- Find most recent supply/demand zone ---
    try:
        mask_zone = (df['Zones_sup_dem'].isin(['Supply', 'Demand']) &
                     df['Zones_sup_dem'].notna() &
                    (df['Zones_sup_dem'].astype(str).str.strip() != '') &
                     df['Zone_Status'].isin(['Valid', 'Tested']))
        if mask_zone.any():
            last_idx = df[mask_zone].index[-1]
            # result['latest_zone'] = {
            #     'timestamp': df.at[last_idx, 'timestamp'],
            #     'zone_type': df.at[last_idx, 'Zones_sup_dem'],
            #     'zone_status': df.at[last_idx, 'Zone_Status'],
            #     'row_index': last_idx
            # }
            result['latest_zone'] = df.iloc[last_idx][:]
    except Exception as e:
        # If any error, leave as None
        pass
    # print("Exiting the new function find_latest_signals", result)
    return result

def look_for_trade_signals(df_1, df_5,df_1min_f'{display_name}):
    result_1min = find_latest_signals(df_1)
    result_5min = find_latest_signals(df_5)
    #result_60min = find_latest_signals(df_60)

    #print("result_60min: ",result_60min)
    last_stored_1min_data = df_1.iloc[-1][:]
    last_stored_5min_data = df_5.iloc[-1][:]
    # last_stored_60min_data = df_60.iloc[-1][:]
    # print("last stored 1min data: ",last_stored_1min_data)
    # print("WMA5: ",last_stored_1min_data["WMA5"])
    # print("WMA10: ",last_stored_1min_data["WMA10"])
    # print("last stored 5min data: ",last_stored_5min_data)
    # print("wma5: ",last_stored_5min_data["WMA5"])
    # print("last stored 60min data: ",last_stored_60min_data)
    
    #When in Puts trade, upward crossover is exit
    #When in Calls trade, downward crossover is exit

    if(result_1min['latest_crossover'] is not None) & (result_5min['latest_crossover'] is not None):
        print("latest crossover: ",result_1min['latest_crossover']['crossover_signal'])
        print("latest zone: ",result_1min['latest_zone']['Zones_sup_dem'])
    
        if(result_1min['latest_crossover']['crossover_signal'] == 'downward'):
            if (last_stored_1min_data["WMA5"] < last_stored_1min_data["WMA10"] & 
                last_stored_5min_data["close"] < last_stored_5min_data["WMA5"]):
                print("Buy Puts")
                return True
                #return tradesymbol,now.time(), true
    #Buy Puts

        elif (result_1min['latest_crossover']['crossover_signal'] == 'upward'):
                if (last_stored_1min_data["WMA5"] > last_stored_1min_data["WMA10"] & 
                    last_stored_5min_data["close"] > last_stored_5min_data["WMA5"]):
                    print("Buy Calls")
                    return True
                #return tradesymbol,now.time(), true
    #Buy Calls
    else:
        return False

def check_for_trade_exit(df_1, df_5,Trade_symbol, Time_trade_taken):

    result_1min = find_latest_signals(df_1)
    result_5min = find_latest_signals(df_5)
    last_stored_1min_data = df_1.iloc[-1][:]
    last_stored_5min_data = df_5.iloc[-1][:]

    if (Trade_symbol contains "PUT"):
        if(result_1min['latest_crossover']['crossover_signal'] == 'upward' &
           result_1min['latest_crossover']['timestamp'] > Time_trade_taken):
            print("Exit Puts")
            return True
        #return tradesymbol,true
    #Exit Puts

    elif (Trade_symbol contains "CALL"):
        if(result_1min['latest_crossover']['crossover_signal'] == 'downward'
           result_1min['latest_crossover']['timestamp'] > Time_trade_taken):
            print("Exit Calls")
            return True
        #return tradesymbol,true
    #Exit Calls


# Example usage
if __name__ == "__main__":

    global options_df = pd.DataFrame()
    
    fetch_interval_seconds = 10 # Define the interval in seconds
    now = datetime.datetime.now()
    current_time = now.time()
    market_close = datetime.time(15, 29)
    market_start = datetime.time(9, 15)

    # Create output directories if they don't exist
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(options_output_dir, exist_ok=True)

    # Initialize data stores and state variables
    data_store = {}  # In-memory store for dataframes: {interval: DataFrame}
    first_run = True
    last_15_min_fetch_minute = -1
    #options_processed = False  # Track if options have been processed in this session

    # Historical data variables
    df_1 = None
    df_5 = None
    df_60 = None
    df_daily = None

    print("=== Enhanced Historical Fetcher with Option Data Integration ===")
    print(f"Historical data output: {output_dir}")
    print(f"Options data output: {options_output_dir}")
    print(f"Options input file: {options_csv_file}")
    print("="*60)
    
    while True:
        now = datetime.datetime.now()
        current_time = now.time()

        # === Option Data Processing (First Run Only) ===
        #if first_run: #and not options_processed:
        print("\n--- Processing Option Data ---")
        try:
            options_results = process_options_csv()
            if options_results["success"]:
                print(f"✓ Options processing completed:")
                print(f"  - Total entries: {options_results['total_entries']}")
                print(f"  - Processed entries: {options_results['processed_entries']}")
                print(f"  - Successful fetches: {options_results['successful_fetches']}")
                print(f"  - Failed fetches: {options_results['failed_fetches']}")
            else:
                print(f"✗ Options processing failed: {options_results.get('error', 'Unknown error')}")
            #options_processed = True
        except Exception as e:
            print(f"✗ Error during options processing: {str(e)}")
            #options_processed = True  # Mark as processed to avoid retry

        # === Historical Data Processing (Existing Functionality) ===
        print(f"\n--- Processing Historical Data ({now.strftime('%H:%M:%S')}) ---")

        # Determine intervals to fetch
        intervals_to_fetch = set()
        if first_run:
            print("→ First run: scheduling all intervals for full historical fetch")
            intervals_to_fetch.update(["1", "5", "60", "daily"])
            first_run = False
        else:
            intervals_to_fetch.update(["1", "5"])

        if now.minute % 15 == 0 and now.minute != last_15_min_fetch_minute:
            print(f"→ Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch")
            intervals_to_fetch.add("60")
            last_15_min_fetch_minute = now.minute

        # Process historical data for each interval
        for interval in intervals_to_fetch:
            print(f"  → Fetching {interval}min data...")
            df, from_date_str, to_date_str = fetch_historical_data(interval)

            # Update Data Store
            if df is not None and not df.empty:
                print(f"    ✓ Fetched {len(df)} new records")
                if interval in data_store:
                    data_store[interval] = pd.concat([data_store[interval], df]).drop_duplicates(subset=['timestamp'], keep='last').sort_values(by='timestamp').reset_index(drop=True)
                else:
                    data_store[interval] = df.sort_values(by='timestamp').reset_index(drop=True)
                print(f"    → Data store for '{interval}' now has {len(data_store[interval])} total records")

            # Process and save data
            if interval in data_store and not data_store[interval].empty:
                df_processed = process_dataframe(df)

                try:
                    interval_suffix = "daily" if interval == "daily" else f"{interval}min"
                    output_filename = f"NIFTY_INDEX_{interval_suffix}_{from_date_str}_to_{to_date_str}_processed.csv"
                    output_path = os.path.join(output_dir, output_filename)

                    df_processed.to_csv(output_path, index=False)
                    print(f"    ✓ Saved processed file: {output_filename}")
                except Exception as e:
                    print(f"    ✗ Error saving processed data for interval {interval}: {str(e)}")

                # Store processed dataframes for decision making
                if df is not None:
                    if interval == "1":
                        df_1 = df_processed
                    elif interval == "5":
                        df_5 = df_processed
                    elif interval == "60":
                        df_60 = df_processed
                    else:
                        df_daily = df_processed
            else:
                print(f"    ✗ No data available for {interval}min interval")

        
        # === Market Hours Check ===
        if (current_time < market_start) | (current_time > market_close):
            print(f"→ Market is closed (current time: {current_time}). Exiting...")
            break

        #   Look for Trade Signals
        Trade_Take = False
        Trade_symbol, Time_trade_taken, Trade_Taken = look_for_trade_signals(df_1, df_5,Options_results)
        print("Trade Taken: ",Trade_Taken)
        if Trade_Taken:
            Trade_exit = check_for_trade_exit(df_1, df_5,Trade_symbol, Time_trade_taken)

        # === Data Summary Display ===
        print("\n--- Current Data Summary ---")
        if df_1 is not None:
            print(f"1min data (last 2 rows):\n{df_1.tail(2)}")
        if df_5 is not None:
            print(f"5min data (last 2 rows):\n{df_5.tail(2)}")
        if df_60 is not None:
            print(f"60min data (last 2 rows):\n{df_60.tail(2)}")

        print(f"\n{'='*60}")
        print(f"Next update in {fetch_interval_seconds} seconds...")
        print(f"{'='*60}\n")
        # last_stored_row = df_1["timestamp"].max()



        # Wait for the specified interval
        time.sleep(fetch_interval_seconds)